{"manifestVersion": "1.0.0", "name": "ComfyUIGen", "displayName": "ComfyUI 图像生成器", "version": "0.3.0", "description": "通过 ComfyUI API 使用自定义工作流生成高质量图像。支持LoRA模型自动管理、质量增强词配置、多种工作流模板。已深度集成到 VCP ToolBox，提供可视化配置界面。", "author": "<PERSON><PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node ComfyUIGen.js"}, "communication": {"protocol": "stdio", "timeout": 300000}, "configSchema": {"COMFYUI_BASE_URL": {"type": "string", "description": "ComfyUI服务器地址", "default": "http://localhost:8188"}, "COMFYUI_API_KEY": {"type": "string", "description": "ComfyUI API密钥（可选，本地部署通常不需要）", "default": ""}, "DEBUG_MODE": {"type": "boolean", "description": "是否启用调试模式，显示详细执行日志", "default": false}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "ComfyUIGenerateImage", "description": "调用此工具通过ComfyUI生成高质量图像。系统会自动应用用户预配置的LoRA模型组合和质量增强词。\n\n🎨 **功能特性**:\n- **智能LoRA集成**: 自动应用用户配置的LoRA模型组合，支持77+ LoRA模型选择\n- **质量增强**: 自动添加质量增强词，提升图像生成质量\n- **工作流支持**: 支持多种预设工作流模板（text2img_basic等）\n- **参数自动化**: 系统自动应用用户预设的技术参数（尺寸、采样器、CFG等）\n\n🤖 **Agent职责**:\n- 专注于创作优质的正面提示词和艺术描述\n- 无需关心技术参数，系统自动处理LoRA组合和质量优化\n- 可选择指定特定工作流模板\n\n📋 **调用格式**:\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent的署名「末」,\ntool_name:「始」ComfyUIGen「末」,\nprompt:「始」(必需) 图像生成的正面提示词，描述想要生成的图像内容、风格、细节等「末」,\nnegative_prompt:「始」(可选) 额外的负面提示词，将与用户配置的负面提示词合并「末」,\nworkflow:「始」(可选) 工作流模板名称，默认使用用户配置的工作流。可选值：text2img_basic, text2img_advanced 等「末」,\nwidth:「始」(可选) 生成图像的宽度，默认使用用户配置的值「末」,\nheight:「始」(可选) 生成图像的高度，默认使用用户配置的值「末」\n<<<[END_TOOL_REQUEST]>>>\n\n⚠️ **重要说明**:\n- 系统会自动合并：用户提示词 + 用户配置的LoRA + 质量增强词\n- 技术参数（模型、尺寸、采样器等）使用用户预设值，但 `width`、`height` 和 `negative_prompt` 可在调用时覆盖或追加\n- 用户需要通过VCP ToolBox的ComfyUI配置界面进行LoRA管理和参数设置", "example": "<<<[TOOL_REQUEST]>>>\nmaid:「始」CreativeAssistant「末」,\ntool_name:「始」ComfyUIGen「末」,\nprompt:「始」a majestic dragon soaring through cloudy skies at sunset, highly detailed scales, fantasy art style, vibrant colors, dramatic lighting, cinematic composition「末」\n<<<[END_TOOL_REQUEST]>>>"}]}, "features": {"loraManagement": {"description": "自动从ComfyUI服务器加载可用LoRA模型，支持多LoRA组合", "capabilities": ["自动扫描ComfyUI服务器上的LoRA模型库", "可视化LoRA选择和配置界面", "独立设置每个LoRA的模型强度和CLIP强度", "启用/禁用LoRA功能，支持LoRA组合预设", "防重复添加机制，智能冲突检测"]}, "qualityEnhancement": {"description": "质量增强词系统，自动提升生成图像质量", "capabilities": ["预设质量增强关键词库", "自动与用户提示词和LoRA标签智能组合", "支持自定义质量标签和风格词", "避免提示词冲突和重复的智能过滤"]}, "workflowManagement": {"description": "工作流模板管理系统", "capabilities": ["支持多种预设工作流模板", "可视化工作流选择和预览", "支持自定义工作流导入和验证", "工作流参数自动映射和占位符替换"]}, "parameterControl": {"description": "全面的生成参数控制系统", "capabilities": ["采样器和调度器选择（20+选项）", "图像尺寸和批次大小设置", "CFG强度和去噪程度调节", "种子控制和随机性管理", "负面提示词管理"]}}, "systemIntegration": {"vcpToolBox": {"description": "深度集成VCP ToolBox配置系统", "configFile": "comfyui-settings.json", "autoSync": true, "features": ["实时配置同步和验证", "可视化参数调节界面", "连接状态实时监控", "错误诊断和智能提示"]}, "comfyuiServer": {"description": "ComfyUI服务器自动发现和配置", "features": ["自动检测可用模型和LoRA列表", "实时连接状态检查", "API兼容性验证", "工作流有效性检测"]}}, "changelog": {"0.3.0": {"date": "2025-08-04", "changes": ["新增：LoRA模型自动扫描和管理系统", "新增：质量增强词智能配置功能", "新增：多LoRA组合支持，独立强度控制", "新增：可视化LoRA选择界面", "优化：配置加载系统，支持复杂参数结构", "优化：提示词智能组合机制，避免冲突", "修复：配置同步和事件绑定问题", "符合：VCP插件开发规范v1.0.0"]}, "0.2.0": {"date": "2024-12-XX", "changes": ["集成到VCP ToolBox配置系统", "添加可视化配置面板", "支持工作流模板管理"]}}, "compatibility": {"vcpVersion": ">=1.0.0", "nodeVersion": ">=14.0.0", "comfyuiVersion": ">=0.3.0"}}