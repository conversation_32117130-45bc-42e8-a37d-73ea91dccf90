{"hourly": [{"fxTime": "2025-08-19T08:00+08:00", "temp": "25", "icon": "101", "text": "多云", "wind360": "29", "windDir": "东北风", "windScale": "1-3", "windSpeed": "9", "humidity": "90", "pop": "45", "precip": "0.0", "pressure": "999", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T09:00+08:00", "temp": "26", "icon": "305", "text": "小雨", "wind360": "50", "windDir": "东北风", "windScale": "1-3", "windSpeed": "9", "humidity": "90", "pop": "70", "precip": "0.26", "pressure": "999", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T10:00+08:00", "temp": "26", "icon": "305", "text": "小雨", "wind360": "83", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "88", "pop": "70", "precip": "0.24", "pressure": "999", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T11:00+08:00", "temp": "26", "icon": "305", "text": "小雨", "wind360": "96", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "87", "pop": "70", "precip": "0.45", "pressure": "999", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T12:00+08:00", "temp": "26", "icon": "305", "text": "小雨", "wind360": "96", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "86", "pop": "70", "precip": "0.44", "pressure": "999", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T13:00+08:00", "temp": "27", "icon": "305", "text": "小雨", "wind360": "95", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "86", "pop": "70", "precip": "0.15", "pressure": "1000", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T14:00+08:00", "temp": "27", "icon": "305", "text": "小雨", "wind360": "95", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "83", "pop": "70", "precip": "0.42", "pressure": "1000", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T15:00+08:00", "temp": "28", "icon": "305", "text": "小雨", "wind360": "96", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "86", "pop": "70", "precip": "0.16", "pressure": "1000", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T16:00+08:00", "temp": "28", "icon": "305", "text": "小雨", "wind360": "98", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "87", "pop": "70", "precip": "0.1", "pressure": "1000", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T17:00+08:00", "temp": "29", "icon": "305", "text": "小雨", "wind360": "98", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "87", "pop": "70", "precip": "0.4", "pressure": "1000", "cloud": "100", "dew": "24"}, {"fxTime": "2025-08-19T18:00+08:00", "temp": "28", "icon": "305", "text": "小雨", "wind360": "81", "windDir": "东风", "windScale": "1-3", "windSpeed": "9", "humidity": "88", "pop": "70", "precip": "1.0", "pressure": "999", "cloud": "99", "dew": "24"}, {"fxTime": "2025-08-19T19:00+08:00", "temp": "27", "icon": "305", "text": "小雨", "wind360": "29", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "89", "pop": "70", "precip": "0.2", "pressure": "1000", "cloud": "98", "dew": "24"}, {"fxTime": "2025-08-19T20:00+08:00", "temp": "27", "icon": "305", "text": "小雨", "wind360": "6", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "88", "pop": "70", "precip": "0.31", "pressure": "999", "cloud": "98", "dew": "23"}, {"fxTime": "2025-08-19T21:00+08:00", "temp": "27", "icon": "302", "text": "雷阵雨", "wind360": "11", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "89", "pop": "70", "precip": "0.27", "pressure": "1000", "cloud": "91", "dew": "23"}, {"fxTime": "2025-08-19T22:00+08:00", "temp": "26", "icon": "302", "text": "雷阵雨", "wind360": "24", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "89", "pop": "70", "precip": "0.25", "pressure": "1000", "cloud": "84", "dew": "23"}, {"fxTime": "2025-08-19T23:00+08:00", "temp": "26", "icon": "302", "text": "雷阵雨", "wind360": "31", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "88", "pop": "70", "precip": "0.33", "pressure": "1001", "cloud": "78", "dew": "23"}, {"fxTime": "2025-08-20T00:00+08:00", "temp": "26", "icon": "302", "text": "雷阵雨", "wind360": "28", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "87", "pop": "70", "precip": "0.43", "pressure": "1000", "cloud": "85", "dew": "22"}, {"fxTime": "2025-08-20T01:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "23", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "88", "pop": "70", "precip": "0.5", "pressure": "1000", "cloud": "91", "dew": "22"}, {"fxTime": "2025-08-20T02:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "20", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "90", "pop": "55", "precip": "2.0", "pressure": "1000", "cloud": "98", "dew": "22"}, {"fxTime": "2025-08-20T03:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "17", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "94", "pop": "70", "precip": "0.13", "pressure": "1000", "cloud": "99", "dew": "22"}, {"fxTime": "2025-08-20T04:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "10", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "89", "pop": "70", "precip": "0.47", "pressure": "1000", "cloud": "99", "dew": "22"}, {"fxTime": "2025-08-20T05:00+08:00", "temp": "24", "icon": "302", "text": "雷阵雨", "wind360": "359", "windDir": "北风", "windScale": "1-3", "windSpeed": "7", "humidity": "86", "pop": "70", "precip": "0.35", "pressure": "999", "cloud": "100", "dew": "22"}, {"fxTime": "2025-08-20T06:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "29", "windDir": "东北风", "windScale": "1-3", "windSpeed": "7", "humidity": "88", "pop": "70", "precip": "0.22", "pressure": "999", "cloud": "96", "dew": "22"}, {"fxTime": "2025-08-20T07:00+08:00", "temp": "25", "icon": "302", "text": "雷阵雨", "wind360": "48", "windDir": "东北风", "windScale": "1-3", "windSpeed": "9", "humidity": "86", "pop": "64", "precip": "6.8", "pressure": "998", "cloud": "93", "dew": "22"}], "warning": [{"id": "10101010020250819051353065358250", "sender": "北京市气象局", "pubTime": "2025-08-19T05:13+08:00", "title": "北京市气象台2025年08月19日05时25分升级发布暴雨黄色预警信号", "startTime": "2025-08-19T05:25+08:00", "endTime": "2025-08-19T21:00+08:00", "status": "update", "level": "黄色", "severity": "Moderate", "severityColor": "Yellow", "type": "1003", "typeName": "暴雨", "urgency": "", "certainty": "", "text": "市气象台2025年08月19日05时25分升级发布暴雨黄色预警信号：预计，当前至19日20时，我市降雨将加强，部分地区小时雨强可达50毫米以上，6小时累计雨量可达70毫米以上，个别点可达150毫米以上，山区及浅山区可能出现山洪、泥石流、滑坡等次生灾害，低洼地区可能出现积水，请注意防范。", "related": "10101010020250818092616916998582"}, {"id": "10101010020250819043320513774393", "sender": "北京市气象局", "pubTime": "2025-08-19T04:33+08:00", "title": "北京市气象台2025年08月19日04时45分发布大风蓝色预警信号", "startTime": "2025-08-19T04:45+08:00", "endTime": "2025-08-19T13:00+08:00", "status": "active", "level": "蓝色", "severity": "Minor", "severityColor": "Blue", "type": "1006", "typeName": "大风", "urgency": "", "certainty": "", "text": "市气象台2025年08月19日04时45分发布大风蓝色预警信号：受雷雨云团影响，预计当前至19日12时，我市大部分地区将出现7、8级短时大风，局地阵风可达9级以上，请注意防范。", "related": ""}, {"id": "10101010020250819030944900477005", "sender": "北京市气象局", "pubTime": "2025-08-19T03:09+08:00", "title": "北京市气象台2025年08月19日03时20分发布雷电黄色预警信号", "startTime": "2025-08-19T03:20+08:00", "endTime": "2025-08-19T21:00+08:00", "status": "active", "level": "黄色", "severity": "Moderate", "severityColor": "Yellow", "type": "1014", "typeName": "雷电", "urgency": "", "certainty": "", "text": "市气象台2025年08月19日03时20分发布雷电黄色预警信号：预计当前至19日20时，我市有雷电活动，并伴有短时强降水，局地有7级左右短时大风，请注意防范。", "related": ""}, {"id": "10101010020250818170523953500675", "sender": "北京市规划和自然资源委员会", "pubTime": "2025-08-18T17:05+08:00", "title": "北京市规划自然资源委联合市气象局8月18日17时发布地质灾害气象风险黄色预警", "startTime": "2025-08-18T20:00+08:00", "endTime": "2025-08-19T20:00+08:00", "status": "active", "level": "黄色", "severity": "Moderate", "severityColor": "Yellow", "type": "1250", "typeName": "地质灾害", "urgency": "", "certainty": "", "text": "市规划自然资源委联合市气象局8月18日17时发布地质灾害气象风险黄色预警：8月18日20时至19日20时，我市怀柔区中部、密云区大部、平谷区北部发生崩塌、滑坡、泥石流等地质灾害的风险较高(黄色预警)；房山区西北部、门头沟区东南部、石景山区、丰台区、海淀区、昌平区、延庆大部、怀柔北部和南部、密云中部和东部、平谷中部和南部有发生崩塌、滑坡、泥石流等地质灾害的风险(蓝色预警)，请注意防范。", "related": ""}], "daily": [{"fxDate": "2025-08-19", "sunrise": "05:31", "sunset": "19:08", "moonrise": "00:33", "moonset": "16:52", "moonPhase": "残月", "moonPhaseIcon": "807", "tempMax": "29", "tempMin": "23", "iconDay": "306", "textDay": "中雨", "iconNight": "302", "textNight": "雷阵雨", "wind360Day": "225", "windDirDay": "西南风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "45", "windDirNight": "东北风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "94", "precip": "3.9", "pressure": "1000", "vis": "21", "cloud": "80", "uvIndex": "2"}, {"fxDate": "2025-08-20", "sunrise": "05:31", "sunset": "19:06", "moonrise": "01:40", "moonset": "17:41", "moonPhase": "残月", "moonPhaseIcon": "807", "tempMax": "31", "tempMin": "24", "iconDay": "302", "textDay": "雷阵雨", "iconNight": "302", "textNight": "雷阵雨", "wind360Day": "45", "windDirDay": "东北风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "90", "windDirNight": "东风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "98", "precip": "6.9", "pressure": "998", "vis": "1", "cloud": "76", "uvIndex": "5"}, {"fxDate": "2025-08-21", "sunrise": "05:32", "sunset": "19:05", "moonrise": "02:53", "moonset": "18:19", "moonPhase": "残月", "moonPhaseIcon": "807", "tempMax": "32", "tempMin": "24", "iconDay": "306", "textDay": "中雨", "iconNight": "104", "textNight": "阴", "wind360Day": "225", "windDirDay": "西南风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "45", "windDirNight": "东北风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "94", "precip": "18.2", "pressure": "998", "vis": "24", "cloud": "80", "uvIndex": "2"}, {"fxDate": "2025-08-22", "sunrise": "05:33", "sunset": "19:03", "moonrise": "04:05", "moonset": "18:51", "moonPhase": "残月", "moonPhaseIcon": "807", "tempMax": "32", "tempMin": "24", "iconDay": "101", "textDay": "多云", "iconNight": "151", "textNight": "多云", "wind360Day": "0", "windDirDay": "北风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "0", "windDirNight": "北风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "90", "precip": "0.0", "pressure": "1001", "vis": "24", "cloud": "4", "uvIndex": "7"}, {"fxDate": "2025-08-23", "sunrise": "05:34", "sunset": "19:02", "moonrise": "05:16", "moonset": "19:16", "moonPhase": "新月", "moonPhaseIcon": "800", "tempMax": "30", "tempMin": "23", "iconDay": "305", "textDay": "小雨", "iconNight": "305", "textNight": "小雨", "wind360Day": "0", "windDirDay": "北风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "90", "windDirNight": "东风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "72", "precip": "0.0", "pressure": "1005", "vis": "25", "cloud": "2", "uvIndex": "3"}, {"fxDate": "2025-08-24", "sunrise": "05:35", "sunset": "19:00", "moonrise": "06:23", "moonset": "19:38", "moonPhase": "蛾眉月", "moonPhaseIcon": "801", "tempMax": "28", "tempMin": "22", "iconDay": "305", "textDay": "小雨", "iconNight": "151", "textNight": "多云", "wind360Day": "0", "windDirDay": "北风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "0", "windDirNight": "北风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "94", "precip": "1.6", "pressure": "1007", "vis": "22", "cloud": "63", "uvIndex": "3"}, {"fxDate": "2025-08-25", "sunrise": "05:36", "sunset": "18:59", "moonrise": "07:27", "moonset": "19:59", "moonPhase": "蛾眉月", "moonPhaseIcon": "801", "tempMax": "30", "tempMin": "23", "iconDay": "104", "textDay": "阴", "iconNight": "104", "textNight": "阴", "wind360Day": "225", "windDirDay": "西南风", "windScaleDay": "1-3", "windSpeedDay": "3", "wind360Night": "225", "windDirNight": "西南风", "windScaleNight": "1-3", "windSpeedNight": "3", "humidity": "80", "precip": "0.0", "pressure": "1008", "vis": "25", "cloud": "3", "uvIndex": "8"}], "moon": null, "airQuality": {"pubTime": "2025-08-19T06:00+08:00", "aqi": "32", "level": "1", "category": "优", "primary": "NA", "pm10": "6", "pm2p5": "3", "no2": "5", "so2": "3", "co": "0.5", "o3": "100"}, "solarAngle": {"code": "200", "solarElevationAngle": "17.47", "solarAzimuthAngle": "87.78", "solarHour": "0648", "hourAngle": "77.77", "refer": {"sources": ["<PERSON><PERSON><PERSON><PERSON>"], "license": ["QWeather Developers License"]}}, "lastUpdate": "2025-08-18T23:07:05.164Z"}