{"displayName": "文本到图像 (基础)", "description": "基础的文本到图像生成工作流", "version": "1.0", "workflow": {"3": {"inputs": {"seed": "{{SEED}}", "steps": "{{STEPS}}", "cfg": "{{CFG}}", "sampler_name": "{{SAMPLER}}", "scheduler": "{{SCHEDULER}}", "denoise": "{{DENOISE}}", "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "4": {"inputs": {"ckpt_name": "{{MODEL}}"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "5": {"inputs": {"width": "{{WIDTH}}", "height": "{{HEIGHT}}", "batch_size": "{{BATCH_SIZE}}"}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "6": {"inputs": {"text": "{{POSITIVE_PROMPT}}", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "7": {"inputs": {"text": "{{NEGATIVE_PROMPT}}", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码（负面）"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}}