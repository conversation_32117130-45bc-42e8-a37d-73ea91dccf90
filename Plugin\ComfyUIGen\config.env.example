# ComfyUI 插件配置示例文件
# 注意：此文件仅作为参考，实际配置请使用前端配置界面
# 前端配置界面会自动生成 comfyui-settings.json 文件

# ==========================================
# 重要提示：
# 推荐使用前端配置界面进行所有设置，该界面提供：
# - 可视化LoRA管理和选择
# - 实时连接测试和状态检查  
# - 参数验证和智能提示
# - 配置自动保存和同步
# ==========================================

# 如果需要通过环境变量覆盖配置，可以设置以下变量：

# ComfyUI服务器地址
# COMFYUI_BASE_URL=http://localhost:8188

# ComfyUI API密钥（通常本地部署无需设置）
# COMFYUI_API_KEY=

# 调试模式（显示详细日志）
# DEBUG_MODE=false

# ==========================================
# 用户配置通过前端界面管理，包括：
# - LoRA模型选择和强度设置
# - 质量增强词配置
# - 工作流模板选择
# - 生成参数（尺寸、采样器等）
# - 负面提示词设置
# ==========================================