{"manifestVersion": "1.0.0", "name": "<PERSON><PERSON>Sear<PERSON>", "displayName": "Tavily 搜索插件", "version": "0.1.0", "description": "使用 Tavily API 进行高级网络搜索。AI可以指定搜索查询、主题、最大结果数，并可选择包含原始内容、图片链接及描述，以及设定搜索时间范围。", "author": "<PERSON><PERSON>", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node TavilySearch.js"}, "communication": {"protocol": "stdio", "timeout": 30000}, "configSchema": {"TavilyKey": "string"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "<PERSON><PERSON>Sear<PERSON>", "description": "调用此工具使用 Tavily API 进行高级网络搜索。默认启用高级搜索模式并返回图片链接。重要提示：请优先使用中文词作为检索词，并带上即时信息（如当前年份、月份等）进行检索，以获得更准确的结果。请在您的回复中，使用以下精确格式来请求搜索，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TavilySearch「末」,\nquery:「始」(必需) 搜索的关键词或问题。「末」,\ntopic:「始」(可选, 默认为 'general') 搜索的主题，例如 'news', 'finance'。如果AI不确定，可以省略此参数或使用 'general'。「末」,\nmax_results:「始」(可选, 默认为 10) 返回的最大结果数量，范围 5-100。「末」,\ninclude_raw_content:「始」(可选) 如果需要获取网页原始内容，请将此值设为 'text' 或 'markdown'。「末」,\nstart_date:「始」(可选) 搜索开始日期，格式为 YYYY-MM-DD。「末」,\nend_date:「始」(可选) 搜索结束日期，格式为 YYYY-MM-DD。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含搜索结果的JSON对象。请基于这些结果回答用户的问题或完成相关任务。\n请优先使用中文词作为检索词，并带上即时信息（如当前年份、月份等）进行检索，以获得更准确的结果。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TavilySearch「末」,\nquery:「始」2025年8月有什么值得关注的科技新闻？「末」,\ntopic:「始」news「末」,\nmax_results:「始」5「末」,\ninclude_raw_content:「始」markdown「末」,\nstart_date:「始」2025-08-01「末」,\nend_date:「始」2025-08-31「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}